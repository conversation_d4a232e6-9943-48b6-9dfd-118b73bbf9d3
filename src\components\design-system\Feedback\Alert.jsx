// src/components/design-system/Feedback/Alert.jsx
import React from 'react';
import { Alert as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lapse, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';

const StyledAlert = styled(MuiAlert)(({ theme }) => ({
  // Additional custom styling if needed
}));

/**
 * Alert component for feedback messages
 * 
 * @param {Object} props
 * @param {'success'|'info'|'warning'|'error'} props.severity - Alert severity
 * @param {'filled'|'outlined'|'standard'} props.variant - Alert variant
 * @param {string} props.title - Alert title
 * @param {boolean} props.closable - Whether alert can be closed
 * @param {function} props.onClose - Close handler
 * @param {boolean} props.open - Whether alert is visible (for controlled alerts)
 * @param {React.ReactNode} props.children - Alert content
 */
const Alert = ({ 
  severity = 'info',
  variant = 'standard',
  title,
  closable = false,
  onClose,
  open = true,
  children,
  ...props 
}) => {
  const [isOpen, setIsOpen] = React.useState(open);

  React.useEffect(() => {
    setIsOpen(open);
  }, [open]);

  const handleClose = () => {
    setIsOpen(false);
    if (onClose) {
      onClose();
    }
  };

  const action = closable ? (
    <IconButton
      aria-label="close"
      color="inherit"
      size="small"
      onClick={handleClose}
    >
      <CloseIcon fontSize="inherit" />
    </IconButton>
  ) : undefined;

  return (
    <Collapse in={isOpen}>
      <StyledAlert
        severity={severity}
        variant={variant}
        action={action}
        {...props}
      >
        {title && <AlertTitle>{title}</AlertTitle>}
        {children}
      </StyledAlert>
    </Collapse>
  );
};

export default Alert;
