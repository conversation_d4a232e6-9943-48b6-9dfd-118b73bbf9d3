-- Insert required tools into the tools table
-- This ensures foreign key constraints are satisfied when logging prompts

-- Insert tools if they don't already exist
INSERT INTO tools (id, name, is_active) VALUES 
('reimagine', 'ReImagine Tool', true),
('img2video', 'Image to Video Tool', true)
ON CONFLICT (id) DO UPDATE SET 
    name = EXCLUDED.name,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Verify the tools were inserted
SELECT id, name, is_active, created_at FROM tools WHERE id IN ('reimagine', 'img2video');
